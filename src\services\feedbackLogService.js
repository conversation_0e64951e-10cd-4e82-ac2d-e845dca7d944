/**
 * 反馈日志记录服务
 * 负责将用户反馈信息记录到.log文件中，包含用户身份信息和时间戳
 */

import { getUserService } from './userService.js'
import { getServerLogService } from './serverLogService.js'
import { serverConfigManager } from '../config/serverConfig.js'

/**
 * 反馈日志记录服务类
 */
class FeedbackLogService {
  constructor() {
    this.logFileName = 'feedback.log'
    this.maxLogSize = 10 * 1024 * 1024 // 10MB
    this.maxLogFiles = 5 // 保留最多5个日志文件
    this.userService = getUserService()

    // 初始化时使用代理配置，实际使用时会动态获取
    this.serverLogService = getServerLogService({
      serverUrl: '/api/logs' // 使用代理路径，会在使用时动态更新
    })
    this.useServerLogging = true // 启用服务器日志记录到 logs/feedback.log 文件
  }

  /**
   * 动态获取并更新服务器URL
   * @param {boolean} preferProxy - 是否优先使用代理
   * @returns {Promise<string|null>} 可用的服务器URL
   */
  async updateServerUrl(preferProxy = true) {
    try {
      const serverUrl = await serverConfigManager.getLogServerUrl(false, preferProxy)
      if (serverUrl) {
        // 更新服务器日志服务的URL
        this.serverLogService = getServerLogService({ serverUrl })
        console.log('🌐 更新服务器地址:', serverUrl)
        console.log('📡 使用代理模式:', serverUrl.startsWith('/'))
        return serverUrl
      } else {
        console.warn('⚠️ 无法获取可用的服务器地址')
        return null
      }
    } catch (error) {
      console.error('❌ 更新服务器地址失败:', error)
      return null
    }
  }

  /**
   * 记录反馈日志
   * @param {Object} feedbackData - 反馈数据
   * @param {string} feedbackData.type - 反馈类型
   * @param {string} feedbackData.content - 反馈内容
   * @param {string} feedbackData.contact - 联系方式（可选）
   * @returns {Promise<boolean>} 记录是否成功
   */
  async logFeedback(feedbackData) {
    console.log('🚀 开始记录反馈日志:', feedbackData)

    try {
      // 验证输入数据
      if (!feedbackData || !feedbackData.content) {
        console.error('❌ 反馈数据无效:', feedbackData)
        return false
      }

      // 获取当前用户信息
      console.log('📋 获取当前用户信息...')
      const currentUser = await this.userService.getCurrentUser()
      if (!currentUser) {
        console.warn('⚠️ 无法获取当前用户信息，使用匿名用户记录反馈')
      } else {
        console.log('✅ 获取到用户信息:', currentUser.username, currentUser.employeeId)
      }

      // 构建日志条目
      console.log('🔧 构建日志条目...')
      const logEntry = this.buildLogEntry(feedbackData, currentUser)
      console.log('✅ 日志条目构建完成:', logEntry.id)

      // 如果启用了服务器日志记录，优先发送到服务器
      if (this.useServerLogging) {
        try {
          // 动态更新服务器地址
          console.log('🔍 检查并更新服务器地址...')
          const serverUrl = await this.updateServerUrl()

          if (serverUrl) {
            console.log('🌐 发送日志到服务器 logs/feedback.log 文件...')
            await this.serverLogService.sendLogToServer(logEntry)
            console.log('✅ 反馈日志已成功写入服务器端 logs/feedback.log 文件:', logEntry.id)
          } else {
            throw new Error('无法连接到日志服务器')
          }
        } catch (error) {
          console.error('❌ 发送日志到服务器失败:', error)
          console.warn('⚠️ 服务器不可用，回退到本地存储...')

          // 如果服务器记录失败，回退到本地存储
          try {
            await this.writeToLogFile(logEntry)
            console.log('✅ 日志已写入本地存储作为备份')
            // 不抛出错误，因为本地存储成功了
          } catch (localError) {
            console.error('❌ 本地存储也失败了:', localError)
            throw new Error(`服务器和本地存储都失败: 服务器错误=${error.message}, 本地错误=${localError.message}`)
          }
        }
      } else {
        // 写入本地日志文件
        console.log('💾 写入本地日志文件...')
        await this.writeToLogFile(logEntry)
        console.log('✅ 本地日志文件写入完成')
      }

      console.log('🎉 反馈日志记录成功:', logEntry.id)
      return true
    } catch (error) {
      console.error('❌ 记录反馈日志失败:', error)
      console.error('错误堆栈:', error.stack)
      return false
    }
  }

  /**
   * 构建日志条目
   * @param {Object} feedbackData - 反馈数据
   * @param {Object} currentUser - 当前用户信息
   * @returns {Object} 日志条目
   */
  buildLogEntry(feedbackData, currentUser) {
    const timestamp = new Date()
    const logEntry = {
      id: this.generateLogId(),
      timestamp: timestamp.toISOString(),
      localTime: timestamp.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'Asia/Shanghai'
      }),
      user: {
        username: currentUser?.username || 'anonymous',
        employeeId: currentUser?.employeeId || 'unknown',
        userKey: currentUser?.userKey || 'anonymous_unknown'
      },
      feedback: {
        type: feedbackData.type,
        content: feedbackData.content,
        contact: feedbackData.contact || ''
      }
    }

    return logEntry
  }

  /**
   * 生成日志ID
   * @returns {string} 日志ID
   */
  generateLogId() {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 11)
    return `feedback_${timestamp}_${random}`
  }

  /**
   * 写入日志文件
   * @param {Object} logEntry - 日志条目
   * @returns {Promise<void>}
   */
  async writeToLogFile(logEntry) {
    try {
      // 格式化日志内容
      const logContent = this.formatLogContent(logEntry)
      
      // 在浏览器环境中，我们使用localStorage作为临时存储
      // 实际生产环境中应该发送到服务器进行文件写入
      await this.writeToStorage(logContent)
      
      // 检查是否需要轮转日志
      await this.checkLogRotation()
      
    } catch (error) {
      console.error('写入日志文件失败:', error)
      throw error
    }
  }

  /**
   * 格式化日志内容
   * @param {Object} logEntry - 日志条目
   * @returns {string} 格式化的日志内容
   */
  formatLogContent(logEntry) {
    const lines = [
      `[${logEntry.localTime}] FEEDBACK_LOG`,
      `ID: ${logEntry.id}`,
      `User: ${logEntry.user.username} (${logEntry.user.employeeId})`,
      `Type: ${logEntry.feedback.type}`,
      `Content: ${logEntry.feedback.content}`,
      `Contact: ${logEntry.feedback.contact}`,
      `---`
    ]

    return lines.join('\n') + '\n'
  }

  /**
   * 写入存储（浏览器环境使用localStorage模拟文件写入）
   * @param {string} logContent - 日志内容
   * @returns {Promise<void>}
   */
  async writeToStorage(logContent) {
    try {
      console.log('📝 开始写入存储...')
      const storageKey = `bodorai_feedback_logs`

      // 检查localStorage是否可用
      if (typeof Storage === 'undefined') {
        throw new Error('浏览器不支持localStorage')
      }

      let existingLogs = localStorage.getItem(storageKey) || ''
      console.log(`📊 现有日志大小: ${existingLogs.length} 字符`)

      // 添加新的日志内容
      const updatedLogs = existingLogs + logContent
      console.log(`📊 更新后日志大小: ${updatedLogs.length} 字符`)

      // 检查存储大小限制
      if (updatedLogs.length > this.maxLogSize) {
        console.log(`⚠️ 日志大小超过限制 (${this.maxLogSize})，进行轮转...`)
        // 如果超过大小限制，保留最新的一半内容
        const halfSize = Math.floor(this.maxLogSize / 2)
        const trimmedLogs = updatedLogs.slice(-halfSize)
        localStorage.setItem(storageKey, trimmedLogs)
        console.log(`✅ 日志已轮转，新大小: ${trimmedLogs.length} 字符`)
      } else {
        localStorage.setItem(storageKey, updatedLogs)
        console.log('✅ 日志已写入localStorage')
      }

      // 验证写入
      const savedLogs = localStorage.getItem(storageKey)
      if (savedLogs && savedLogs.includes(logContent.substring(0, 50))) {
        console.log('✅ 写入验证成功')
      } else {
        console.warn('⚠️ 写入验证失败')
      }

      // 同时创建一个可下载的日志文件
      console.log('📁 创建可下载日志文件...')
      await this.createDownloadableLogFile(logContent)
      console.log('✅ 可下载日志文件创建完成')

    } catch (error) {
      console.error('❌ 写入存储失败:', error)
      throw error
    }
  }

  /**
   * 创建可下载的日志文件
   * @param {string} logContent - 日志内容
   * @returns {Promise<void>}
   */
  async createDownloadableLogFile(logContent) {
    try {
      // 获取现有的日志文件内容
      const existingContent = await this.getExistingLogFileContent()
      const fullContent = existingContent + logContent
      
      // 创建Blob对象
      const blob = new Blob([fullContent], { type: 'text/plain;charset=utf-8' })
      
      // 存储到一个特殊的键中，用于后续下载
      const reader = new FileReader()
      reader.onload = () => {
        localStorage.setItem('bodorai_feedback_log_file', reader.result)
      }
      reader.readAsDataURL(blob)
      
    } catch (error) {
      console.error('创建可下载日志文件失败:', error)
    }
  }

  /**
   * 获取现有日志文件内容
   * @returns {Promise<string>} 现有日志内容
   */
  async getExistingLogFileContent() {
    try {
      const dataUrl = localStorage.getItem('bodorai_feedback_log_file')
      if (!dataUrl) return ''

      // 从data URL中提取文本内容
      const response = await fetch(dataUrl)
      const text = await response.text()
      return text
    } catch (error) {
      console.error('获取现有日志文件内容失败:', error)
      return ''
    }
  }

  /**
   * 检查日志轮转
   * @returns {Promise<void>}
   */
  async checkLogRotation() {
    try {
      const storageKey = `bodorai_feedback_logs`
      const logs = localStorage.getItem(storageKey) || ''

      // 如果日志大小超过限制，进行轮转
      if (logs.length > this.maxLogSize) {
        await this.rotateLogFiles()
      }
    } catch (error) {
      console.error('检查日志轮转失败:', error)
    }
  }

  /**
   * 轮转日志文件
   * @returns {Promise<void>}
   */
  async rotateLogFiles() {
    try {
      const storageKey = `bodorai_feedback_logs`
      const currentLogs = localStorage.getItem(storageKey) || ''

      if (currentLogs.length === 0) return

      // 生成轮转文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const rotatedKey = `bodorai_feedback_logs_${timestamp}`

      // 保存当前日志到轮转文件
      localStorage.setItem(rotatedKey, currentLogs)

      // 清空当前日志
      localStorage.setItem(storageKey, '')

      // 清理旧的轮转文件
      await this.cleanupOldLogFiles()

      console.log('日志文件轮转完成:', rotatedKey)
    } catch (error) {
      console.error('轮转日志文件失败:', error)
    }
  }

  /**
   * 清理旧的日志文件
   * @returns {Promise<void>}
   */
  async cleanupOldLogFiles() {
    try {
      const allKeys = Object.keys(localStorage)
      const logFileKeys = allKeys.filter(key => key.startsWith('bodorai_feedback_logs_'))

      // 按时间戳排序（最新的在前）
      logFileKeys.sort((a, b) => {
        const timestampA = a.replace('bodorai_feedback_logs_', '')
        const timestampB = b.replace('bodorai_feedback_logs_', '')
        return timestampB.localeCompare(timestampA)
      })

      // 删除超过保留数量的旧文件
      if (logFileKeys.length > this.maxLogFiles) {
        const filesToDelete = logFileKeys.slice(this.maxLogFiles)
        filesToDelete.forEach(key => {
          localStorage.removeItem(key)
          console.log('删除旧日志文件:', key)
        })
      }
    } catch (error) {
      console.error('清理旧日志文件失败:', error)
    }
  }

  /**
   * 下载日志文件
   * @param {string} filename - 文件名（可选）
   * @returns {Promise<void>}
   */
  async downloadLogFile(filename = null) {
    try {
      const content = await this.getExistingLogFileContent()
      if (!content) {
        console.warn('没有日志内容可下载')
        return
      }

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')
      const defaultFilename = `feedback_${timestamp}.log`
      const finalFilename = filename || defaultFilename

      // 创建下载链接
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = finalFilename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      URL.revokeObjectURL(url)

      console.log('日志文件下载完成:', finalFilename)
    } catch (error) {
      console.error('下载日志文件失败:', error)
    }
  }

  /**
   * 获取日志统计信息
   * @returns {Promise<Object>} 日志统计信息
   */
  async getLogStats() {
    try {
      const storageKey = `bodorai_feedback_logs`
      const logs = localStorage.getItem(storageKey) || ''

      // 统计日志条目数量
      const logEntries = logs.split('---').filter(entry => entry.trim())
      const totalEntries = logEntries.length

      // 统计不同类型的反馈
      const typeStats = {}
      const userStats = {}

      logEntries.forEach(entry => {
        const typeMatch = entry.match(/Type: (\w+)/)
        const userMatch = entry.match(/User: ([^(]+) \(([^)]+)\)/)

        if (typeMatch) {
          const type = typeMatch[1]
          typeStats[type] = (typeStats[type] || 0) + 1
        }

        if (userMatch) {
          const username = userMatch[1].trim()
          userStats[username] = (userStats[username] || 0) + 1
        }
      })

      // 获取轮转文件信息
      const allKeys = Object.keys(localStorage)
      const rotatedFiles = allKeys.filter(key => key.startsWith('bodorai_feedback_logs_'))

      return {
        totalEntries,
        currentLogSize: logs.length,
        maxLogSize: this.maxLogSize,
        typeStats,
        userStats,
        rotatedFiles: rotatedFiles.length,
        lastRotation: rotatedFiles.length > 0 ? rotatedFiles[0].replace('bodorai_feedback_logs_', '') : null
      }
    } catch (error) {
      console.error('获取日志统计信息失败:', error)
      return {
        totalEntries: 0,
        currentLogSize: 0,
        maxLogSize: this.maxLogSize,
        typeStats: {},
        userStats: {},
        rotatedFiles: 0,
        lastRotation: null
      }
    }
  }

  /**
   * 清空所有日志
   * @returns {Promise<boolean>} 清空是否成功
   */
  async clearAllLogs() {
    try {
      // 清空当前日志
      localStorage.removeItem('bodorai_feedback_logs')
      localStorage.removeItem('bodorai_feedback_log_file')

      // 清空所有轮转文件
      const allKeys = Object.keys(localStorage)
      const logFileKeys = allKeys.filter(key => key.startsWith('bodorai_feedback_logs_'))

      logFileKeys.forEach(key => {
        localStorage.removeItem(key)
      })

      console.log('所有反馈日志已清空')
      return true
    } catch (error) {
      console.error('清空日志失败:', error)
      return false
    }
  }

  /**
   * 启用服务器日志记录
   * @param {string} serverUrl - 服务器URL（可选）
   */
  enableServerLogging(serverUrl = null) {
    this.useServerLogging = true
    if (serverUrl) {
      this.serverLogService = getServerLogService({ serverUrl })
    }
    console.log('服务器日志记录已启用')
  }

  /**
   * 禁用服务器日志记录
   */
  disableServerLogging() {
    this.useServerLogging = false
    console.log('服务器日志记录已禁用')
  }

  /**
   * 检查服务器连接状态
   * @returns {Promise<boolean>} 连接是否正常
   */
  async checkServerConnection() {
    if (!this.useServerLogging) {
      return false
    }

    try {
      return await this.serverLogService.checkServerConnection()
    } catch (error) {
      console.error('检查服务器连接失败:', error)
      return false
    }
  }

  /**
   * 获取配置信息
   * @returns {Object} 配置信息
   */
  getConfig() {
    return {
      logFileName: this.logFileName,
      maxLogSize: this.maxLogSize,
      maxLogFiles: this.maxLogFiles,
      useServerLogging: this.useServerLogging,
      serverUrl: this.serverLogService?.serverUrl || null
    }
  }
}

// 创建单例实例
let feedbackLogServiceInstance = null

/**
 * 获取反馈日志服务实例
 * @returns {FeedbackLogService} 反馈日志服务实例
 */
export function getFeedbackLogService() {
  if (!feedbackLogServiceInstance) {
    feedbackLogServiceInstance = new FeedbackLogService()
  }
  return feedbackLogServiceInstance
}

// 导出服务类
export { FeedbackLogService }
