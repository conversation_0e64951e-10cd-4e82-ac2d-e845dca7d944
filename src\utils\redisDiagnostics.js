/**
 * Redis诊断工具
 * 用于诊断Redis连接问题和系统状态
 */

import { getRedisConfig } from '../config/redis.js'
import logger from './logger.js'

/**
 * Redis诊断器类
 */
class RedisDiagnostics {
  constructor() {
    this.config = getRedisConfig()
    this.diagnosticResults = []
  }

  /**
   * 执行完整诊断
   */
  async runFullDiagnostics() {
    console.log('🔍 开始Redis系统诊断...')
    this.diagnosticResults = []

    // 1. 检查配置
    await this.checkConfiguration()

    // 2. 检查网络连接
    await this.checkNetworkConnectivity()

    // 3. 检查Redis HTTP代理
    await this.checkRedisProxy()

    // 4. 检查Redis服务器
    await this.checkRedisServer()

    // 5. 检查浏览器环境
    await this.checkBrowserEnvironment()

    // 6. 生成诊断报告
    const report = this.generateDiagnosticReport()
    
    console.log('📋 诊断完成，结果如下:')
    console.log(report)
    
    return {
      success: this.diagnosticResults.every(result => result.success),
      results: this.diagnosticResults,
      report
    }
  }

  /**
   * 检查配置
   */
  async checkConfiguration() {
    const result = {
      test: '配置检查',
      success: true,
      details: [],
      errors: []
    }

    try {
      // 检查Redis配置
      if (!this.config.host) {
        result.success = false
        result.errors.push('Redis主机地址未配置')
      } else {
        result.details.push(`✅ Redis主机: ${this.config.host}`)
      }

      if (!this.config.port) {
        result.success = false
        result.errors.push('Redis端口未配置')
      } else {
        result.details.push(`✅ Redis端口: ${this.config.port}`)
      }

      if (!this.config.password) {
        result.details.push('⚠️ Redis密码未配置（可能不需要）')
      } else {
        result.details.push('✅ Redis密码已配置')
      }

      result.details.push(`✅ 数据库: ${this.config.db}`)
      result.details.push(`✅ 连接超时: ${this.config.connectTimeout}ms`)

    } catch (error) {
      result.success = false
      result.errors.push(`配置检查失败: ${error.message}`)
    }

    this.diagnosticResults.push(result)
  }

  /**
   * 检查网络连接
   */
  async checkNetworkConnectivity() {
    const result = {
      test: '网络连接检查',
      success: true,
      details: [],
      errors: []
    }

    try {
      // 检查是否在线
      if (navigator.onLine) {
        result.details.push('✅ 浏览器显示在线状态')
      } else {
        result.success = false
        result.errors.push('❌ 浏览器显示离线状态')
      }

      // 尝试ping一个公共服务
      try {
        const response = await fetch('https://httpbin.org/get', {
          method: 'GET',
          mode: 'cors',
          timeout: 5000
        })
        
        if (response.ok) {
          result.details.push('✅ 外网连接正常')
        } else {
          result.details.push('⚠️ 外网连接异常')
        }
      } catch (error) {
        result.details.push(`⚠️ 外网连接测试失败: ${error.message}`)
      }

    } catch (error) {
      result.success = false
      result.errors.push(`网络检查失败: ${error.message}`)
    }

    this.diagnosticResults.push(result)
  }

  /**
   * 检查Redis HTTP代理
   */
  async checkRedisProxy() {
    const result = {
      test: 'Redis HTTP代理检查',
      success: true,
      details: [],
      errors: []
    }

    try {
      // 先尝试代理路径
      const proxyUrl = this.getProxyUrl()
      result.details.push(`代理路径: ${proxyUrl}`)

      let proxySuccess = false

      // 尝试连接代理路径
      try {
        const response = await fetch(`${proxyUrl}/ping`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        })

        if (response.ok) {
          const data = await response.text()
          if (data === 'PONG') {
            result.details.push('✅ Redis HTTP代理路径响应正常')
            proxySuccess = true
          } else {
            result.details.push(`⚠️ Redis HTTP代理路径响应异常: ${data}`)
          }
        } else {
          result.details.push(`⚠️ Redis HTTP代理路径连接失败: ${response.status} ${response.statusText}`)
        }
      } catch (error) {
        result.details.push(`⚠️ Redis HTTP代理路径连接错误: ${error.message}`)
      }

      // 如果代理路径失败，尝试直连代理服务器
      if (!proxySuccess) {
        const directProxyUrl = this.getDirectProxyUrl()
        result.details.push(`回退到直连代理: ${directProxyUrl}`)

        try {
          const response = await fetch(`${directProxyUrl}/ping`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            },
            timeout: 10000
          })

          if (response.ok) {
            const data = await response.text()
            if (data === 'PONG') {
              result.details.push('✅ Redis HTTP直连代理响应正常')
              proxySuccess = true
            } else {
              result.errors.push(`❌ Redis HTTP直连代理响应异常: ${data}`)
            }
          } else {
            result.errors.push(`❌ Redis HTTP直连代理连接失败: ${response.status} ${response.statusText}`)
          }
        } catch (error) {
          if (error.name === 'TypeError' && error.message.includes('fetch')) {
            result.errors.push('❌ 无法连接到Redis HTTP代理服务器，请确认代理服务器已启动')
            result.details.push('💡 请运行: cd proxy-server && npm start')
          } else {
            result.errors.push(`❌ Redis HTTP直连代理连接错误: ${error.message}`)
          }
        }
      }

      result.success = proxySuccess

    } catch (error) {
      result.success = false
      result.errors.push(`Redis HTTP代理检查失败: ${error.message}`)
    }

    this.diagnosticResults.push(result)
  }

  /**
   * 检查Redis服务器
   */
  async checkRedisServer() {
    const result = {
      test: 'Redis服务器检查',
      success: true,
      details: [],
      errors: []
    }

    try {
      result.details.push(`Redis服务器: ${this.config.host}:${this.config.port}`)
      
      // 这里只能通过代理检查，无法直接连接Redis
      result.details.push('ℹ️ Redis服务器状态需要通过HTTP代理检查')
      
      // 如果代理连接成功，说明Redis服务器可能也是正常的
      const proxyResult = this.diagnosticResults.find(r => r.test === 'Redis HTTP代理检查')
      if (proxyResult && proxyResult.success) {
        result.details.push('✅ 通过代理确认Redis服务器可访问')
      } else {
        result.success = false
        result.errors.push('❌ 无法通过代理确认Redis服务器状态')
      }

    } catch (error) {
      result.success = false
      result.errors.push(`Redis服务器检查失败: ${error.message}`)
    }

    this.diagnosticResults.push(result)
  }

  /**
   * 检查浏览器环境
   */
  async checkBrowserEnvironment() {
    const result = {
      test: '浏览器环境检查',
      success: true,
      details: [],
      errors: []
    }

    try {
      // 检查必要的API支持
      if (typeof fetch !== 'undefined') {
        result.details.push('✅ Fetch API支持')
      } else {
        result.success = false
        result.errors.push('❌ Fetch API不支持')
      }

      if (typeof localStorage !== 'undefined') {
        result.details.push('✅ LocalStorage支持')
      } else {
        result.success = false
        result.errors.push('❌ LocalStorage不支持')
      }

      if (typeof BroadcastChannel !== 'undefined') {
        result.details.push('✅ BroadcastChannel支持')
      } else {
        result.details.push('⚠️ BroadcastChannel不支持（影响跨标签页同步）')
      }

      // 检查CORS设置
      result.details.push('ℹ️ 请确保Redis HTTP代理服务器已配置CORS')

      // 检查浏览器版本
      result.details.push(`浏览器: ${navigator.userAgent}`)

    } catch (error) {
      result.success = false
      result.errors.push(`浏览器环境检查失败: ${error.message}`)
    }

    this.diagnosticResults.push(result)
  }

  /**
   * 获取代理URL
   * 优先使用代理路径
   */
  getProxyUrl() {
    // 优先使用代理路径
    return '/redis'
  }

  /**
   * 获取直连代理URL（用于回退）
   */
  getDirectProxyUrl() {
    // 获取当前页面的主机名
    const currentHost = window.location.hostname

    // 在开发环境中使用当前访问的主机地址
    if (process.env.NODE_ENV === 'development') {
      return `http://${currentHost}:4001/redis`
    }

    // 生产环境中使用配置的主机
    return `http://${this.config.host}:4001/redis`
  }

  /**
   * 生成诊断报告
   */
  generateDiagnosticReport() {
    const successCount = this.diagnosticResults.filter(r => r.success).length
    const totalCount = this.diagnosticResults.length
    
    let report = `\n📊 Redis诊断报告\n`
    report += `${'='.repeat(50)}\n`
    report += `总体状态: ${successCount}/${totalCount} 项检查通过\n\n`

    this.diagnosticResults.forEach((result, index) => {
      const status = result.success ? '✅' : '❌'
      report += `${index + 1}. ${status} ${result.test}\n`
      
      if (result.details.length > 0) {
        result.details.forEach(detail => {
          report += `   ${detail}\n`
        })
      }
      
      if (result.errors.length > 0) {
        result.errors.forEach(error => {
          report += `   ${error}\n`
        })
      }
      
      report += '\n'
    })

    // 添加建议
    report += `💡 故障排除建议:\n`
    report += `${'='.repeat(50)}\n`
    
    const failedTests = this.diagnosticResults.filter(r => !r.success)
    if (failedTests.length === 0) {
      report += `✅ 所有检查都通过了！Redis系统应该可以正常工作。\n`
    } else {
      failedTests.forEach(test => {
        if (test.test === 'Redis HTTP代理检查') {
          report += `1. 启动Redis HTTP代理服务器:\n`
          report += `   cd proxy-server && npm install && npm start\n`
          report += `2. 确认代理服务器运行在端口4001\n`
          report += `3. 检查防火墙设置\n\n`
        }
        
        if (test.test === 'Redis服务器检查') {
          report += `1. 确认Redis服务器运行状态\n`
          report += `2. 检查Redis服务器配置\n`
          report += `3. 验证网络连接\n\n`
        }
      })
    }

    return report
  }

  /**
   * 快速连接测试
   */
  async quickConnectionTest() {
    try {
      // 先尝试代理路径
      const proxyUrl = this.getProxyUrl()
      const response = await fetch(`${proxyUrl}/ping`, {
        method: 'GET',
        timeout: 5000
      })

      if (response.ok) {
        const data = await response.text()
        if (data === 'PONG') {
          return true
        }
      }

      // 如果代理路径失败，尝试直连
      const directProxyUrl = this.getDirectProxyUrl()
      const directResponse = await fetch(`${directProxyUrl}/ping`, {
        method: 'GET',
        timeout: 5000
      })

      if (directResponse.ok) {
        const data = await directResponse.text()
        return data === 'PONG'
      }

      return false
    } catch (error) {
      console.error('快速连接测试失败:', error)
      return false
    }
  }
}

// 创建单例实例
let diagnostics = null

/**
 * 获取Redis诊断器实例
 */
export const getRedisDiagnostics = () => {
  if (!diagnostics) {
    diagnostics = new RedisDiagnostics()
  }
  return diagnostics
}

// 暴露到全局用于调试
if (typeof window !== 'undefined') {
  window.runRedisDiagnostics = async () => {
    const diagnostics = getRedisDiagnostics()
    return await diagnostics.runFullDiagnostics()
  }
  
  window.quickRedisTest = async () => {
    const diagnostics = getRedisDiagnostics()
    return await diagnostics.quickConnectionTest()
  }
}

export default RedisDiagnostics
