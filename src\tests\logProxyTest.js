/**
 * 日志代理功能测试
 * 用于验证日志代理配置是否正常工作
 */

import { getServerConfig, getLogServerUrl, getAvailableLogServerUrl } from '../config/serverConfig.js'
import { getFeedbackLogService } from '../services/feedbackLogService.js'

/**
 * 测试日志代理配置
 */
export async function testLogProxyConfig() {
  console.log('🧪 开始测试日志代理配置...')
  
  try {
    // 1. 测试服务器配置
    console.log('\n1️⃣ 测试服务器配置:')
    const config = getServerConfig()
    console.log('服务器配置:', config.logServer)
    
    // 2. 测试URL生成
    console.log('\n2️⃣ 测试URL生成:')
    const proxyUrl = getLogServerUrl(true)  // 使用代理
    const directUrl = getLogServerUrl(false) // 直连
    console.log('代理URL:', proxyUrl)
    console.log('直连URL:', directUrl)
    
    // 3. 测试可用性检测
    console.log('\n3️⃣ 测试可用性检测:')
    const availableUrl = await getAvailableLogServerUrl(true) // 优先代理
    console.log('可用URL:', availableUrl)
    
    // 4. 测试日志服务
    console.log('\n4️⃣ 测试日志服务:')
    const feedbackLogService = getFeedbackLogService()
    const serverUrl = await feedbackLogService.updateServerUrl(true)
    console.log('日志服务URL:', serverUrl)
    
    // 5. 测试发送日志
    console.log('\n5️⃣ 测试发送日志:')
    const testFeedback = {
      type: 'test',
      content: '这是一个代理测试日志',
      contact: '<EMAIL>'
    }
    
    const result = await feedbackLogService.logFeedback(testFeedback)
    console.log('日志发送结果:', result)
    
    console.log('\n✅ 日志代理配置测试完成')
    return true
    
  } catch (error) {
    console.error('❌ 日志代理配置测试失败:', error)
    return false
  }
}

/**
 * 测试代理连接
 */
export async function testProxyConnection() {
  console.log('🔗 测试代理连接...')
  
  try {
    const response = await fetch('/api/logs/health', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 代理连接成功:', data)
      return true
    } else {
      console.error('❌ 代理连接失败:', response.status, response.statusText)
      return false
    }
  } catch (error) {
    console.error('❌ 代理连接异常:', error)
    return false
  }
}

/**
 * 测试直连服务器
 */
export async function testDirectConnection() {
  console.log('🔗 测试直连服务器...')
  
  try {
    const config = getServerConfig()
    const { protocol, host, port } = config.logServer
    const url = `${protocol}//${host}:${port}/api/logs/health`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 直连服务器成功:', data)
      return true
    } else {
      console.error('❌ 直连服务器失败:', response.status, response.statusText)
      return false
    }
  } catch (error) {
    console.error('❌ 直连服务器异常:', error)
    return false
  }
}

/**
 * 运行所有测试
 */
export async function runAllLogProxyTests() {
  console.log('🚀 开始运行所有日志代理测试...')
  
  const results = {
    config: false,
    proxy: false,
    direct: false
  }
  
  // 测试配置
  results.config = await testLogProxyConfig()
  
  // 测试代理连接
  results.proxy = await testProxyConnection()
  
  // 测试直连
  results.direct = await testDirectConnection()
  
  console.log('\n📊 测试结果汇总:')
  console.log('配置测试:', results.config ? '✅ 通过' : '❌ 失败')
  console.log('代理连接:', results.proxy ? '✅ 通过' : '❌ 失败')
  console.log('直连服务器:', results.direct ? '✅ 通过' : '❌ 失败')
  
  const allPassed = Object.values(results).every(result => result)
  console.log('\n总体结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败')
  
  return results
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数挂载到全局对象
  window.logProxyTests = {
    testLogProxyConfig,
    testProxyConnection,
    testDirectConnection,
    runAllLogProxyTests
  }
  
  console.log('📝 日志代理测试函数已加载，可以在控制台中调用:')
  console.log('- window.logProxyTests.runAllLogProxyTests() // 运行所有测试')
  console.log('- window.logProxyTests.testProxyConnection() // 测试代理连接')
  console.log('- window.logProxyTests.testDirectConnection() // 测试直连')
}
