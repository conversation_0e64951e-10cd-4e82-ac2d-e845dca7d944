/**
 * 服务器配置文件
 * 用于管理不同环境下的服务器地址配置
 */

/**
 * 获取当前环境的服务器配置
 * @returns {Object} 服务器配置对象
 */
export function getServerConfig() {
  const protocol = window.location.protocol

  // 统一配置 - 所有环境都优先使用vite代理
  const config = {
    logServer: {
      port: 4002,
      host: '***********', // 统一使用代理目标服务器地址
      protocol: protocol,
      // 强制使用代理路径，避免CORS问题
      useProxy: true,
      proxyPath: '/api/logs'
    },
    redisProxy: {
      port: 4001,
      host: '***********', // 统一使用代理目标服务器地址
      protocol: protocol,
      // 强制使用代理路径，避免CORS问题
      useProxy: true,
      proxyPath: '/redis'
    }
  }

  return config
}

/**
 * 获取日志服务器URL
 * @param {boolean} useProxy - 是否使用代理模式
 * @returns {string} 日志服务器完整URL
 */
export function getLogServerUrl(useProxy = null) {
  const config = getServerConfig()
  const { protocol, host, port, useProxy: configUseProxy, proxyPath } = config.logServer

  // 如果明确指定使用代理，或者配置中启用了代理
  const shouldUseProxy = useProxy !== null ? useProxy : configUseProxy

  if (shouldUseProxy && proxyPath) {
    // 使用代理路径，相对于当前域名
    return proxyPath
  } else {
    // 直接连接到服务器
    return `${protocol}//${host}:${port}/api/logs`
  }
}

/**
 * 获取Redis代理服务器URL
 * @param {boolean} useProxy - 是否使用代理模式
 * @returns {string} Redis代理服务器完整URL
 */
export function getRedisProxyUrl(useProxy = null) {
  const config = getServerConfig()
  const { protocol, host, port, useProxy: configUseProxy, proxyPath } = config.redisProxy

  // 如果明确指定使用代理，或者配置中启用了代理
  const shouldUseProxy = useProxy !== null ? useProxy : configUseProxy

  if (shouldUseProxy && proxyPath) {
    // 使用代理路径，相对于当前域名
    return proxyPath
  } else {
    // 直接连接到代理服务器
    return `${protocol}//${host}:${port}/redis`
  }
}

/**
 * 检测服务器可用性
 * @param {string} url - 服务器URL
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<boolean>} 服务器是否可用
 */
export async function checkServerAvailability(url, timeout = 5000) {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    
    const response = await fetch(url + '/health', {
      method: 'GET',
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    return response.ok
  } catch (error) {
    console.warn(`服务器 ${url} 不可用:`, error.message)
    return false
  }
}

/**
 * 获取可用的日志服务器URL
 * 优先使用vite代理路径，确保所有请求都通过代理
 * @returns {Promise<string|null>} 可用的服务器URL，如果都不可用则返回null
 */
export async function getAvailableLogServerUrl() {
  const config = getServerConfig()
  const { useProxy, proxyPath } = config.logServer

  // 强制使用代理路径，避免CORS问题
  if (useProxy && proxyPath) {
    console.log(`🔍 检测日志代理服务器: ${proxyPath}`)

    const isAvailable = await checkServerAvailability(proxyPath)
    if (isAvailable) {
      console.log(`✅ 找到可用日志代理服务器: ${proxyPath}`)
      return proxyPath
    } else {
      console.warn('⚠️ 日志代理服务器不可用，请检查vite代理配置')
    }
  }

  console.warn('⚠️ 没有找到可用的日志服务器')
  return null
}

/**
 * 获取可用的Redis代理服务器URL
 * 优先使用vite代理路径，确保所有请求都通过代理
 * @returns {Promise<string|null>} 可用的服务器URL，如果都不可用则返回null
 */
export async function getAvailableRedisProxyUrl() {
  const config = getServerConfig()
  const { useProxy, proxyPath } = config.redisProxy

  // 强制使用代理路径，避免CORS问题
  if (useProxy && proxyPath) {
    console.log(`🔍 检测Redis代理服务器: ${proxyPath}`)

    const isAvailable = await checkRedisProxyAvailability(proxyPath)
    if (isAvailable) {
      console.log(`✅ 找到可用Redis代理服务器: ${proxyPath}`)
      return proxyPath
    } else {
      console.warn('⚠️ Redis代理服务器不可用，请检查vite代理配置')
    }
  }

  console.warn('⚠️ 没有找到可用的Redis代理服务器')
  return null
}

/**
 * 检测Redis代理服务器可用性
 * @param {string} url - 服务器URL
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<boolean>} 服务器是否可用
 */
export async function checkRedisProxyAvailability(url, timeout = 5000) {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    const response = await fetch(url + '/ping', {
      method: 'GET',
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (response.ok) {
      const data = await response.text()
      return data === 'PONG'
    }

    return false
  } catch (error) {
    console.warn(`Redis代理服务器 ${url} 不可用:`, error.message)
    return false
  }
}

/**
 * 动态配置管理器
 */
export class DynamicServerConfig {
  constructor() {
    this.cachedLogServerUrl = null
    this.cachedRedisProxyUrl = null
    this.lastLogCheckTime = 0
    this.lastRedisCheckTime = 0
    this.checkInterval = 5 * 60 * 1000 // 5分钟重新检测一次
  }
  
  /**
   * 获取日志服务器URL（带缓存）
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<string|null>} 服务器URL
   */
  async getLogServerUrl(forceRefresh = false) {
    const now = Date.now()

    // 如果有缓存且未过期，直接返回
    if (!forceRefresh &&
        this.cachedLogServerUrl &&
        (now - this.lastLogCheckTime) < this.checkInterval) {
      return this.cachedLogServerUrl
    }

    // 重新检测可用服务器
    this.cachedLogServerUrl = await getAvailableLogServerUrl()
    this.lastLogCheckTime = now

    return this.cachedLogServerUrl
  }

  /**
   * 获取Redis代理服务器URL（带缓存）
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<string|null>} 服务器URL
   */
  async getRedisProxyUrl(forceRefresh = false) {
    const now = Date.now()

    // 如果有缓存且未过期，直接返回
    if (!forceRefresh &&
        this.cachedRedisProxyUrl &&
        (now - this.lastRedisCheckTime) < this.checkInterval) {
      return this.cachedRedisProxyUrl
    }

    // 重新检测可用服务器
    this.cachedRedisProxyUrl = await getAvailableRedisProxyUrl()
    this.lastRedisCheckTime = now

    return this.cachedRedisProxyUrl
  }
  
  /**
   * 清除缓存
   */
  clearCache() {
    this.cachedLogServerUrl = null
    this.cachedRedisProxyUrl = null
    this.lastLogCheckTime = 0
    this.lastRedisCheckTime = 0
  }

  /**
   * 清除日志服务器缓存
   */
  clearLogCache() {
    this.cachedLogServerUrl = null
    this.lastLogCheckTime = 0
  }

  /**
   * 清除Redis代理缓存
   */
  clearRedisCache() {
    this.cachedRedisProxyUrl = null
    this.lastRedisCheckTime = 0
  }
}

// 创建全局配置管理器实例
export const serverConfigManager = new DynamicServerConfig()

// 导出默认配置获取函数
export default {
  getServerConfig,
  getLogServerUrl,
  getRedisProxyUrl,
  checkServerAvailability,
  checkRedisProxyAvailability,
  getAvailableLogServerUrl,
  getAvailableRedisProxyUrl,
  serverConfigManager
}
